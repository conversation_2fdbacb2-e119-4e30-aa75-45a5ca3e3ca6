/* ========== 慕夏风格全新原创设计 ========== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

::-webkit-scrollbar {
  display: none;
}

body {
  font-family: 'Cinzel', 'Playfair Display', serif;
  background:
    radial-gradient(circle at 25% 25%, rgba(218, 165, 32, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(205, 133, 63, 0.12) 0%, transparent 50%),
    linear-gradient(135deg, #f7f0e1 0%, #f2e8d4 50%, #ede0c7 100%);
  overflow: hidden;
  cursor: none !important;
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 隐藏所有元素的默认鼠标 */
*, *::before, *::after {
  cursor: none !important;
}

/* ========== 水平滚动系统 ========== */

.horizontal-scroll-container {
  display: flex;
  width: 400vw; /* 4个页面 */
  height: 100vh;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.page {
  width: 100vw;
  height: 100vh;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  opacity: 1; /* 所有页面始终可见 */
}

/* 慕夏风格装饰边框 */
.mucha-border-frame {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 12px solid transparent;
  border-image: 
    linear-gradient(45deg, 
      #DAA520 0%, 
      #CD853F 25%, 
      #B8860B 50%, 
      #8B4513 75%, 
      #DAA520 100%
    ) 1;
  pointer-events: none;
  z-index: 1000;
}

.mucha-border-frame::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 2px solid rgba(218, 165, 32, 0.3);
  border-radius: 8px;
}

/* 慕夏风格箭头光标 */
.mucha-cursor {
  position: fixed;
  width: 20px;
  height: 24px;
  z-index: 2000;
  pointer-events: none;
  transform: translate(-2px, -2px);
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 完全清除旧样式 */
  background: none !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  /* 使用SVG箭头 */
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%238B4513" stroke="%23DAA520" stroke-width="1"/></svg>') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  filter: drop-shadow(1px 2px 4px rgba(139, 69, 19, 0.4));
}

/* 清除伪元素 */
.mucha-cursor::before,
.mucha-cursor::after {
  display: none !important;
}

/* 鼠标悬停状态 */
.mucha-cursor.hover {
  transform: translate(-2px, -2px) scale(1.2);
  filter: drop-shadow(2px 3px 8px rgba(139, 69, 19, 0.6));
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%23CD853F" stroke="%23DAA520" stroke-width="1.5"/></svg>') !important;
}

/* 滚动状态 */
.mucha-cursor.scrolling {
  animation: cursor-pulse 0.4s ease-in-out infinite alternate;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%23DAA520" stroke="%23F4E4BC" stroke-width="1.5"/></svg>') !important;
}

/* 滚动方向指示 */
.mucha-cursor.scroll-left {
  animation: arrow-left 0.6s ease-in-out;
}

.mucha-cursor.scroll-right {
  animation: arrow-right 0.6s ease-in-out;
}

/* 箭头动画 */
@keyframes cursor-pulse {
  0% {
    transform: translate(-2px, -2px) scale(1);
    filter: drop-shadow(0 2px 8px rgba(139, 69, 19, 0.4));
  }
  100% {
    transform: translate(-2px, -2px) scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(139, 69, 19, 0.6));
  }
}

@keyframes arrow-left {
  0%, 100% { transform: translate(-2px, -2px); }
  50% { transform: translate(-6px, -2px); }
}

@keyframes arrow-right {
  0%, 100% { transform: translate(-2px, -2px); }
  50% { transform: translate(2px, -2px); }
}

/* 鼠标悬停状态 */
.mucha-cursor.hover {
  width: 50px;
  height: 50px;
  background:
    radial-gradient(circle,
      rgba(218, 165, 32, 1) 0%,
      rgba(205, 133, 63, 0.8) 40%,
      rgba(139, 69, 19, 0.4) 70%,
      transparent 100%
    );
  box-shadow:
    0 0 20px rgba(218, 165, 32, 0.7),
    inset 0 0 15px rgba(255, 255, 255, 0.4),
    0 0 40px rgba(218, 165, 32, 0.3);
}

.mucha-cursor.hover::after {
  opacity: 1;
}

/* 滚动状态 */
.mucha-cursor.scrolling {
  width: 35px;
  height: 35px;
  border-color: #DAA520;
  animation: cursor-pulse 0.6s ease-in-out infinite alternate;
}

@keyframes cursor-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 0 15px rgba(218, 165, 32, 0.5),
      inset 0 0 10px rgba(255, 255, 255, 0.3);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow:
      0 0 25px rgba(218, 165, 32, 0.8),
      inset 0 0 15px rgba(255, 255, 255, 0.5);
  }
}

/* 主容器 */
.mucha-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 顶部装饰条 */
.mucha-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: 
    linear-gradient(90deg, 
      transparent 0%, 
      rgba(218, 165, 32, 0.1) 50%, 
      transparent 100%
    );
  border-bottom: 2px solid rgba(139, 69, 19, 0.2);
}

.mucha-ornament {
  width: 100px;
  height: 40px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 40"><path d="M10,20 Q25,5 50,20 Q75,35 90,20" fill="none" stroke="%23CD853F" stroke-width="2"/><circle cx="20" cy="15" r="3" fill="%23DAA520"/><circle cx="80" cy="25" r="3" fill="%23DAA520"/></svg>') center/contain no-repeat;
}

.mucha-title {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B4513;
  text-align: center;
  margin: 0 3rem;
  letter-spacing: 3px;
  text-shadow: 
    2px 2px 4px rgba(139, 69, 19, 0.3),
    0 0 15px rgba(218, 165, 32, 0.2);
  background: linear-gradient(135deg, #8B4513 0%, #CD853F 50%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 中央艺术面板 */
.mucha-art-panel {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

#mucha-canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 左侧装饰文字 */
.mucha-text-left {
  position: absolute;
  left: 5%;
  top: 30%;
  z-index: 10;
  text-align: left;
}

.mucha-text-right {
  position: absolute;
  right: 5%;
  top: 30%;
  z-index: 10;
  text-align: right;
}

.mucha-heading {
  font-family: 'Cinzel', serif;
  font-size: 4rem;
  font-weight: 600;
  color: #8B4513;
  margin: 0.5rem 0;
  text-shadow: 
    3px 3px 6px rgba(139, 69, 19, 0.4),
    0 0 20px rgba(218, 165, 32, 0.3);
  background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: perspective(500px) rotateY(-5deg);
}

.mucha-subheading {
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 400;
  font-style: italic;
  color: #CD853F;
  margin: 0.5rem 0;
  text-shadow: 1px 1px 3px rgba(205, 133, 63, 0.3);
}

.mucha-text-right .mucha-heading {
  transform: perspective(500px) rotateY(5deg);
}

/* 底部艺术家信息 */
.mucha-artist-info {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 10;
}

.mucha-name {
  font-family: 'Cinzel', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
  text-shadow: 
    2px 2px 6px rgba(139, 69, 19, 0.5),
    0 0 25px rgba(218, 165, 32, 0.3);
  background: linear-gradient(135deg, #8B4513 0%, #CD853F 50%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.mucha-subtitle {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  font-weight: 400;
  font-style: italic;
  color: #CD853F;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.3);
}

/* 底部装饰花卉 */
.mucha-footer {
  height: 80px;
  background: 
    linear-gradient(90deg, 
      transparent 0%, 
      rgba(218, 165, 32, 0.15) 50%, 
      transparent 100%
    );
  border-top: 2px solid rgba(139, 69, 19, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.mucha-floral-pattern {
  width: 300px;
  height: 60px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 60"><g><circle cx="60" cy="30" r="8" fill="%23DAA520" opacity="0.7"/><circle cx="120" cy="30" r="6" fill="%23CD853F" opacity="0.6"/><circle cx="180" cy="30" r="8" fill="%23DAA520" opacity="0.7"/><circle cx="240" cy="30" r="6" fill="%23CD853F" opacity="0.6"/><path d="M40,30 Q60,10 80,30 Q60,50 40,30" fill="none" stroke="%238B4513" stroke-width="1.5" opacity="0.5"/><path d="M100,30 Q120,15 140,30 Q120,45 100,30" fill="none" stroke="%238B4513" stroke-width="1.5" opacity="0.5"/><path d="M160,30 Q180,10 200,30 Q180,50 160,30" fill="none" stroke="%238B4513" stroke-width="1.5" opacity="0.5"/><path d="M220,30 Q240,15 260,30 Q240,45 220,30" fill="none" stroke="%238B4513" stroke-width="1.5" opacity="0.5"/></g></svg>') center/contain no-repeat;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .mucha-heading {
    font-size: 3rem;
  }
  
  .mucha-name {
    font-size: 2.5rem;
  }
  
  .mucha-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .mucha-text-left,
  .mucha-text-right {
    display: none;
  }
  
  .mucha-heading {
    font-size: 2.5rem;
  }
  
  .mucha-name {
    font-size: 2rem;
  }
}

/* ========== 滚动进度指示器 ========== */

.scroll-progress-indicator {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1500;
  width: 4px;
  height: 200px;
  background: rgba(139, 69, 19, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 0%;
  background: linear-gradient(180deg, #DAA520 0%, #CD853F 50%, #8B4513 100%);
  border-radius: 2px;
  transition: height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 10px rgba(218, 165, 32, 0.4);
}

.progress-indicator-label {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-family: 'Cinzel', serif;
  font-size: 0.8rem;
  color: #8B4513;
  background: rgba(244, 228, 188, 0.9);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  border: 1px solid rgba(139, 69, 19, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.scroll-progress-indicator:hover .progress-indicator-label {
  opacity: 1;
}

/* ========== 页面2: 关于页面 ========== */

.page-about .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
  gap: 4rem;
}

.about-text {
  flex: 1;
  max-width: 50%;
}

.mucha-main-title {
  font-family: 'Cinzel', serif;
  font-size: 4rem;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 2rem;
  text-shadow: 
    3px 3px 6px rgba(139, 69, 19, 0.4),
    0 0 25px rgba(218, 165, 32, 0.3);
  background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.mucha-description {
  margin-bottom: 3rem;
}

.intro-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.4rem;
  line-height: 1.8;
  color: #8B4513;
  margin-bottom: 1.5rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.2);
}

.skills-section {
  margin-top: 2rem;
}

.skills-title {
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 600;
  color: #CD853F;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(205, 133, 63, 0.3);
}

.skill-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.skill-item {
  font-family: 'Playfair Display', serif;
  font-size: 1.1rem;
  color: #8B4513;
  padding: 0.8rem 1.2rem;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.1));
  border: 1px solid rgba(139, 69, 19, 0.2);
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.skill-item:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.2));
  border-color: rgba(139, 69, 19, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

.decorative-art {
  flex: 1;
  max-width: 40%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mucha-portrait {
  width: 300px;
  height: 400px;
  background: 
    linear-gradient(135deg, 
      rgba(218, 165, 32, 0.3) 0%, 
      rgba(205, 133, 63, 0.2) 50%, 
      rgba(139, 69, 19, 0.1) 100%
    );
  border: 3px solid #CD853F;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 10px 30px rgba(139, 69, 19, 0.3),
    inset 0 0 20px rgba(218, 165, 32, 0.2);
}

.mucha-portrait::before {
  content: '';
  position: absolute;
  top: 20%;
  left: 20%;
  right: 20%;
  bottom: 20%;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><circle cx="100" cy="60" r="30" fill="%23CD853F" opacity="0.6"/><ellipse cx="100" cy="120" rx="40" ry="60" fill="%23DAA520" opacity="0.4"/><path d="M60,150 Q100,120 140,150 Q100,180 60,150" fill="%238B4513" opacity="0.5"/></svg>') center/contain no-repeat;
}

.mucha-portrait::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400"><g opacity="0.3"><path d="M50,50 Q150,20 250,50 Q150,80 50,50" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M50,350 Q150,320 250,350 Q150,380 50,350" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M20,100 Q50,200 20,300" stroke="%23CD853F" stroke-width="2" fill="none"/><path d="M280,100 Q250,200 280,300" stroke="%23CD853F" stroke-width="2" fill="none"/></g></svg>') center/cover no-repeat;
}

/* ========== 页面3: 作品集页面 ========== */

.page-portfolio .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 5%;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  max-width: 1000px;
  width: 100%;
}

.portfolio-item {
  perspective: 1000px;
}

.portfolio-card {
  background: 
    linear-gradient(135deg, 
      rgba(244, 228, 188, 0.9) 0%, 
      rgba(232, 213, 183, 0.8) 100%
    );
  border: 2px solid rgba(139, 69, 19, 0.3);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  cursor: pointer;
}

.portfolio-card:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow: 
    0 15px 40px rgba(139, 69, 19, 0.3),
    0 0 25px rgba(218, 165, 32, 0.2);
  border-color: rgba(139, 69, 19, 0.5);
}

.card-ornament {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 30px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30"><path d="M10,15 Q20,5 30,15 Q40,25 50,15" fill="none" stroke="%23CD853F" stroke-width="2"/><circle cx="15" cy="12" r="2" fill="%23DAA520"/><circle cx="45" cy="18" r="2" fill="%23DAA520"/></svg>') center/contain no-repeat;
}

.portfolio-card h3 {
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B4513;
  margin: 2rem 0 1rem 0;
  text-shadow: 1px 1px 3px rgba(139, 69, 19, 0.3);
}

.portfolio-card p {
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #CD853F;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.2);
}

/* ========== 页面4: 联系页面 ========== */

.page-contact .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
  gap: 4rem;
}

.contact-content {
  display: flex;
  width: 100%;
  gap: 4rem;
  align-items: center;
}

.contact-text {
  flex: 1;
  max-width: 60%;
}

.contact-description {
  font-family: 'Playfair Display', serif;
  font-size: 1.3rem;
  line-height: 1.8;
  color: #8B4513;
  margin-bottom: 3rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.2);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.1));
  border: 1px solid rgba(139, 69, 19, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.2));
  border-color: rgba(139, 69, 19, 0.4);
  transform: translateX(8px);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.2);
}

.contact-icon {
  font-size: 2.2rem;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #DAA520, #CD853F);
  border-radius: 50%;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.contact-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(218, 165, 32, 0.4);
}

.contact-info h3 {
  font-family: 'Cinzel', serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #8B4513;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.2);
}

.contact-info p {
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #CD853F;
  margin: 0;
  text-shadow: 1px 1px 1px rgba(205, 133, 63, 0.2);
}

.contact-art {
  flex: 1;
  max-width: 35%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mucha-contact-ornament {
  width: 250px;
  height: 350px;
  background: 
    linear-gradient(45deg, 
      rgba(218, 165, 32, 0.2) 0%, 
      rgba(205, 133, 63, 0.15) 50%, 
      rgba(139, 69, 19, 0.1) 100%
    );
  border: 2px solid rgba(139, 69, 19, 0.3);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.mucha-contact-ornament::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 250 350"><g opacity="0.4"><circle cx="125" cy="80" r="25" fill="none" stroke="%23DAA520" stroke-width="2"/><circle cx="125" cy="175" r="35" fill="none" stroke="%23CD853F" stroke-width="2"/><circle cx="125" cy="270" r="25" fill="none" stroke="%23DAA520" stroke-width="2"/><path d="M50,50 Q125,25 200,50" fill="none" stroke="%238B4513" stroke-width="2"/><path d="M50,300 Q125,275 200,300" fill="none" stroke="%238B4513" stroke-width="2"/><path d="M30,100 Q125,150 220,100" fill="none" stroke="%23CD853F" stroke-width="1.5"/><path d="M30,250 Q125,200 220,250" fill="none" stroke="%23CD853F" stroke-width="1.5"/></g></svg>') center/cover no-repeat;
}

.mucha-contact-ornament::after {
  content: '';
  position: absolute;
  top: 20%;
  left: 20%;
  right: 20%;
  bottom: 20%;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 210"><g opacity="0.6"><ellipse cx="75" cy="60" rx="20" ry="30" fill="%23DAA520"/><ellipse cx="75" cy="105" rx="30" ry="20" fill="%23CD853F"/><ellipse cx="75" cy="150" rx="20" ry="30" fill="%23DAA520"/></g></svg>') center/contain no-repeat;
}

/* ========== 页面5: 项目展示页面 ========== */

.page-projects .mucha-content-panel {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 350px;
  grid-template-rows: 1fr auto;
  gap: 2rem;
  padding: 1rem 3%;
  position: relative;
  overflow: hidden;
}

.projects-showcase {
  position: relative;
  background: 
    radial-gradient(circle at 30% 30%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(205, 133, 63, 0.08) 0%, transparent 50%);
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 12px;
  overflow: hidden;
  cursor: grab;
}

.projects-showcase:active {
  cursor: grabbing;
}

.project-info-panel {
  background: 
    linear-gradient(135deg, 
      rgba(244, 228, 188, 0.95) 0%, 
      rgba(232, 213, 183, 0.9) 100%
    );
  border: 2px solid rgba(139, 69, 19, 0.3);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 10px 30px rgba(139, 69, 19, 0.2),
    inset 0 0 20px rgba(218, 165, 32, 0.1);
  transition: all 0.5s ease;
}

.project-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.project-title {
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #8B4513;
  margin: 0;
  text-shadow: 
    2px 2px 4px rgba(139, 69, 19, 0.3),
    0 0 15px rgba(218, 165, 32, 0.2);
  background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.project-description {
  flex: 1;
}

.project-description p {
  font-family: 'Playfair Display', serif;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #8B4513;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

.project-tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin: 1rem 0;
}

.tech-tag {
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: #8B4513;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border: 1px solid rgba(139, 69, 19, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tech-tag:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.3), rgba(205, 133, 63, 0.25));
  border-color: rgba(139, 69, 19, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

.project-links {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.project-link {
  font-family: 'Cinzel', serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  color: white;
  background: linear-gradient(135deg, #8B4513 0%, #CD853F 100%);
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.project-link:hover {
  background: linear-gradient(135deg, #CD853F 0%, #DAA520 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
}

.project-link.secondary {
  background: transparent;
  color: #8B4513;
  border-color: #CD853F;
  box-shadow: none;
}

.project-link.secondary:hover {
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.1), rgba(218, 165, 32, 0.1));
  border-color: #8B4513;
}

.project-navigation {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1rem;
  background: 
    linear-gradient(90deg, 
      transparent 0%, 
      rgba(218, 165, 32, 0.1) 50%, 
      transparent 100%
    );
  border-radius: 12px;
  border: 1px solid rgba(139, 69, 19, 0.2);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  border: 2px solid transparent;
  min-width: 80px;
}

.nav-item:hover {
  background: rgba(218, 165, 32, 0.15);
  border-color: rgba(139, 69, 19, 0.3);
  transform: translateY(-3px);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border-color: rgba(139, 69, 19, 0.4);
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
}

.nav-icon {
  font-size: 1.8rem;
  transition: transform 0.3s ease;
  color: #8B4513;
  text-shadow: 1px 1px 3px rgba(139, 69, 19, 0.3);
  filter: drop-shadow(0 2px 4px rgba(218, 165, 32, 0.2));
}

.nav-item:hover .nav-icon {
  transform: scale(1.15) rotate(8deg);
  color: #DAA520;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.4);
}

.nav-item.active .nav-icon {
  color: #DAA520;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.4);
  transform: scale(1.1);
}

.nav-item span {
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: #8B4513;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

/* ========== 高级动画效果 ========== */

.mouse-follower {
  position: fixed;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(218, 165, 32, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1999;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.transition-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 1500;
  overflow: hidden;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(218, 165, 32, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(218, 165, 32, 0.6);
  }
}

/* 为不同元素添加动画类 */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 文字出现效果 */
.text-reveal {
  overflow: hidden;
}

.text-reveal span {
  display: inline-block;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.text-reveal.active span {
  opacity: 1;
  transform: translateY(0);
}

/* 磁性效果 */
.magnetic {
  transition: transform 0.3s ease;
}

.magnetic:hover {
  transform: scale(1.05);
}

/* 波纹效果 */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(218, 165, 32, 0.6) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple.active::before {
  width: 300px;
  height: 300px;
}

/* 视差效果 */
.parallax {
  transition: transform 1s ease;
}

/* 动画延迟类 */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* ========== A1项目融合样式 - 简化卡片展开效果 ========== */

/* 开场页面基础样式 */
#page4 {
  background: linear-gradient(135deg, #f4e4bc 0%, #e8d5b7 50%, #d4af8c 100%);
  background-attachment: fixed;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  overflow: hidden;
}

/* A1色卡样式 */
.a1-color-card {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  transition: all 0.3s ease;
}

/* 慕夏风格色彩定义 */
.a1-color-5 {
  background: linear-gradient(135deg, #b8956a 0%, #a67c5a 100%);
  box-shadow: 0 8px 32px rgba(184, 149, 106, 0.3);
}

.a1-color-banner-1 {
  background: linear-gradient(135deg, #f4e4bc 0%, #d4af8c 100%);
  box-shadow: 0 4px 16px rgba(244, 228, 188, 0.4);
}

.a1-color-banner-2 {
  background: linear-gradient(135deg, #c9a96e 0%, #8b6f47 100%);
  box-shadow: 0 4px 16px rgba(201, 169, 110, 0.4);
}

/* 简化的覆盖层样式 */
.a1-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(45deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  background-image:
    radial-gradient(circle at 20% 30%, rgba(212, 175, 140, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(244, 228, 188, 0.1) 0%, transparent 50%);
  color: #f4e4bc;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 1;
}

.a1-loader {
  text-align: center;
}

.a1-loader h1 {
  font-family: "Cinzel", "Playfair Display", serif;
  font-size: 3.5rem;
  font-weight: 600;
  line-height: 0.9;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-image: linear-gradient(0deg, #8b6f47, #8b6f47 50%, #f4e4bc 0);
  background-size: 100% 200%;
  background-position: 0% 100%;
  color: #8b6f47;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 3px;
  margin: 0.2em 0;
}

/* 中心卡片样式 */
.a1-hero-card {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  opacity: 0;
  z-index: 2;
}

/* 导航栏样式 */
.a1-nav {
  position: fixed;
  top: 0;
  width: 100vw;
  display: flex;
  padding: 1.5em 3em;
  gap: 2em;
  background: rgba(244, 228, 188, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 2px solid #d4af8c;
  box-shadow: 0 4px 20px rgba(212, 175, 140, 0.3);
  transform: translateY(-100%);
  z-index: 10;
}

.a1-nav > * {
  flex: 1;
}

.a1-links {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.a1-links a {
  text-decoration: none;
  text-transform: capitalize;
  font-family: "Cinzel", serif;
  font-size: 1rem;
  color: #8b6f47;
  transition: all 0.3s ease;
  position: relative;
}

.a1-links a:hover {
  color: #d4af8c;
  transform: translateY(-2px);
}

.a1-nav-logo {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.a1-nav-logo a {
  font-family: "Cinzel", serif;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 0.9;
  color: #8b6f47;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  text-decoration: none;
  transition: all 0.3s ease;
}

.a1-nav-logo a:hover {
  color: #c9a96e;
  transform: scale(1.05);
}

/* 横幅图片样式 */
.a1-banner-img {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 180px;
  height: 240px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0,0,0,0.2);
  z-index: 3;
}

/* 介绍文字样式 */
.a1-intro-copy {
  position: absolute;
  top: 45%;
  transform: translateY(-50%);
  width: 100%;
  padding: 0 8em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5;
}

.a1-intro-copy h3 {
  font-family: "Cinzel", serif;
  font-size: 2.2rem;
  font-weight: 600;
  color: #8b6f47;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  line-height: 1;
  opacity: 0;
  transform: translateY(50px);
}

/* 标题样式 */
.a1-title {
  position: absolute;
  bottom: 12%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
}

.a1-title h1 {
  font-family: "Cinzel", serif;
  font-size: 4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b6f47 0%, #d4af8c 50%, #f4e4bc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.1);
  line-height: 0.9;
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
}

/* 装饰性背景元素 */
#page4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 140, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(244, 228, 188, 0.1) 0%, transparent 25%);
  pointer-events: none;
  z-index: 1;
}

/* 跳过按钮样式 */
.skip-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  padding: 12px 24px;
  background: rgba(139, 111, 71, 0.9);
  color: #f4e4bc;
  border: 2px solid #d4af8c;
  border-radius: 25px;
  font-family: "Cinzel", serif;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 10001;
  opacity: 0;
  animation: fadeInSkip 0.5s ease forwards 2s;
}

.skip-button:hover {
  background: rgba(212, 175, 140, 0.9);
  color: #8b6f47;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(139, 111, 71, 0.3);
}

@keyframes fadeInSkip {
  to {
    opacity: 1;
  }
}

/* 横幅图片样式 */
.a1-banner-img {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 180px;
  height: 240px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0,0,0,0.2);
  z-index: 3;
}

.a1-banner-img-1 {
  z-index: 4;
}

.a1-banner-img-2 {
  z-index: 4;
}

/* 介绍文字样式 */
.a1-intro-copy {
  position: absolute;
  top: 45%;
  transform: translateY(-50%);
  width: 100%;
  padding: 0 8em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5;
}

.a1-intro-copy::before {
  content: '';
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 60px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 60"><path d="M15,30 Q75,10 150,30 T285,30" stroke="%23d4af8c" stroke-width="3" fill="none" opacity="0.7"/><circle cx="150" cy="30" r="4" fill="%23d4af8c" opacity="0.8"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
}

.a1-intro-copy h3 {
  font-family: "Cinzel", serif;
  font-size: 2.2rem;
  font-weight: 600;
  color: #8b6f47;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  line-height: 1;
  opacity: 0;
  transform: translateY(50px);
}

/* 标题样式 */
.a1-title {
  position: absolute;
  bottom: 12%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
}

.a1-title h1 {
  font-family: "Cinzel", serif;
  font-size: 4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b6f47 0%, #d4af8c 50%, #f4e4bc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.1);
  line-height: 0.9;
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
}

/* 装饰性背景元素 */
#page4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 140, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(244, 228, 188, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 40% 70%, rgba(139, 111, 71, 0.05) 0%, transparent 20%);
  pointer-events: none;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .a1-loader h1 {
    font-size: 2.5rem;
  }

  .a1-hero-card {
    width: 150px;
    height: 150px;
  }

  .a1-banner-img {
    width: 120px;
    height: 160px;
  }

  .a1-intro-copy {
    padding: 0 4em;
  }

  .a1-intro-copy h3 {
    font-size: 1.6rem;
  }

  .a1-title h1 {
    font-size: 2.8rem;
  }

  .a1-nav {
    padding: 1em 2em;
  }
}

/* ========== A1项目融合样式 - 卡片展开效果 ========== */

/* 页面4基础样式 */
#page4 {
  background: linear-gradient(135deg, #f4e4bc 0%, #e8d5b7 50%, #d4af8c 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

/* A1色卡样式 */
.a1-color-card {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  transition: opacity 0.3s ease;
}

/* 慕夏风格色彩定义 */
.a1-color-1 {
  background: linear-gradient(135deg, #f4e4bc 0%, #e8d5b7 100%);
}

.a1-color-2 {
  background: linear-gradient(135deg, #e8d5b7 0%, #d4af8c 100%);
}

.a1-color-3 {
  background: linear-gradient(135deg, #d4af8c 0%, #c9a96e 100%);
}

.a1-color-4 {
  background: linear-gradient(135deg, #c9a96e 0%, #b8956a 100%);
}

.a1-color-5 {
  background: linear-gradient(135deg, #b8956a 0%, #a67c5a 100%);
}

.a1-color-6 {
  background: linear-gradient(135deg, #a67c5a 0%, #8b6f47 100%);
}

.a1-color-7 {
  background: linear-gradient(135deg, #8b6f47 0%, #7a6b53 100%);
}

.a1-color-8 {
  background: linear-gradient(135deg, #7a6b53 0%, #6b5b73 100%);
}

.a1-color-9 {
  background: linear-gradient(135deg, #6b5b73 0%, #8b7e8e 100%);
}

.a1-color-banner-1 {
  background: linear-gradient(135deg, #f4e4bc 0%, #d4af8c 100%);
}

.a1-color-banner-2 {
  background: linear-gradient(135deg, #c9a96e 0%, #8b6f47 100%);
}

/* A1覆盖层样式 */
.a1-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  padding: 2em;
  background: linear-gradient(45deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  background-image:
    radial-gradient(circle at 20% 30%, rgba(212, 175, 140, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(244, 228, 188, 0.1) 0%, transparent 50%);
  color: #f4e4bc;
  display: flex;
  gap: 2em;
  overflow: hidden;
  border: 3px solid #d4af8c;
  box-shadow: inset 0 0 50px rgba(212, 175, 140, 0.2);
  z-index: 1000;
}

.a1-projects,
.a1-loader,
.a1-locations {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2rem;
}

.a1-loader {
  align-items: center;
  gap: 0;
}

.a1-loader h1 {
  text-align: center;
  text-transform: capitalize;
  font-family: "Cinzel", "Playfair Display", serif;
  font-size: 3rem;
  font-weight: 600;
  line-height: 0.9;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-image: linear-gradient(0deg, #8b6f47, #8b6f47 50%, #f4e4bc 0);
  background-size: 100% 200%;
  background-position: 0% 100%;
  color: #8b6f47;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 2px;
}

.a1-projects-header,
.a1-project-item,
.a1-locations-header,
.a1-location-item {
  opacity: 0;
  display: flex;
  gap: 2em;
  padding: 0.5em 0;
  border-bottom: 1px solid rgba(212, 175, 140, 0.3);
}

.a1-projects-header > *,
.a1-locations-header > * {
  flex: 1;
}

.a1-locations {
  align-items: center;
}

.a1-locations-header,
.a1-location-item {
  width: 50%;
}

.a1-project-item,
.a1-location-item {
  color: #d4af8c;
  font-weight: 300;
}

.a1-overlay p {
  text-transform: capitalize;
  font-family: "Cinzel", serif;
  font-size: 0.9rem;
  font-style: italic;
  color: #8b6f47;
}