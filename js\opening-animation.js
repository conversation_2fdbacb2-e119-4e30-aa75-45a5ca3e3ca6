// 开场动画 - A1风格卡片展开效果
class OpeningAnimation {
    constructor() {
        this.isAnimationComplete = false;
        this.timeline = null;
        this.init();
    }

    init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startAnimation());
        } else {
            this.startAnimation();
        }
    }

    startAnimation() {
        // 确保GSAP已加载
        if (typeof gsap === 'undefined') {
            console.error('GSAP not loaded');
            return;
        }

        // 隐藏主要内容
        this.hideMainContent();
        
        // 创建动画时间线
        this.createTimeline();
    }

    hideMainContent() {
        // 隐藏水平滚动容器
        const scrollContainer = document.querySelector('.horizontal-scroll-container');
        if (scrollContainer) {
            gsap.set(scrollContainer, { opacity: 0, pointerEvents: 'none' });
        }

        // 隐藏其他UI元素
        gsap.set(['.scroll-progress-indicator', '.mucha-cursor'], { opacity: 0 });
    }

    createTimeline() {
        this.timeline = gsap.timeline({
            onComplete: () => this.completeAnimation()
        });

        // 第一阶段：Logo动画
        this.timeline
            .to('.a1-logo-line-1', {
                backgroundPosition: '0% 0%',
                color: '#fff',
                duration: 1.2,
                ease: 'power2.out',
                delay: 0.5
            })
            .to('.a1-logo-line-2', {
                backgroundPosition: '0% 0%',
                color: '#fff',
                duration: 1.2,
                ease: 'power2.out',
                delay: 0.3
            }, '<0.4');

        // 第二阶段：覆盖层淡出，卡片出现
        this.timeline
            .to('.a1-overlay', {
                opacity: 0,
                duration: 1,
                ease: 'power2.inOut',
                delay: 1
            })
            .to('.a1-hero-card', {
                opacity: 1,
                scale: 1,
                duration: 0.8,
                ease: 'back.out(1.7)'
            }, '<0.5');

        // 第三阶段：卡片放大
        this.timeline
            .to('.a1-hero-card', {
                scale: 4,
                duration: 2,
                ease: 'power2.inOut',
                delay: 0.8
            });

        // 第四阶段：导航栏出现
        this.timeline
            .to('.a1-nav', {
                y: '0%',
                duration: 1,
                ease: 'power3.out'
            }, '<0.5');

        // 第五阶段：横幅图片出现
        this.timeline
            .to('.a1-banner-img-1', {
                scale: 1,
                left: '35%',
                rotate: -15,
                duration: 1.2,
                ease: 'back.out(1.7)'
            }, '<0.3')
            .to('.a1-banner-img-2', {
                scale: 1,
                left: '65%',
                rotate: 15,
                duration: 1.2,
                ease: 'back.out(1.7)'
            }, '<0.2');

        // 第六阶段：文字出现
        this.timeline
            .to('.a1-intro-copy h3', {
                opacity: 1,
                y: 0,
                duration: 1,
                stagger: 0.2,
                ease: 'power3.out'
            }, '<0.5')
            .to('.a1-title h1', {
                opacity: 1,
                y: 0,
                duration: 1.2,
                ease: 'power3.out'
            }, '<0.3');

        // 最终阶段：添加装饰效果
        this.timeline
            .to('.a1-intro-copy::before', {
                opacity: 1,
                duration: 0.8,
                ease: 'power2.out'
            }, '<0.5');
    }

    completeAnimation() {
        this.isAnimationComplete = true;
        
        // 延迟3秒后自动进入主要内容
        setTimeout(() => {
            this.transitionToMainContent();
        }, 3000);

        // 添加点击事件监听器，允许用户提前进入
        document.addEventListener('click', () => {
            if (this.isAnimationComplete) {
                this.transitionToMainContent();
            }
        });

        // 添加键盘事件监听器
        document.addEventListener('keydown', (e) => {
            if (this.isAnimationComplete && (e.key === 'Enter' || e.key === ' ')) {
                this.transitionToMainContent();
            }
        });
    }

    transitionToMainContent() {
        // 防止重复执行
        if (this.transitionStarted) return;
        this.transitionStarted = true;

        // 创建过渡动画
        const transitionTimeline = gsap.timeline({
            onComplete: () => {
                // 移除开场页面
                const page4 = document.getElementById('page4');
                if (page4) {
                    page4.style.display = 'none';
                }
            }
        });

        // 淡出开场内容
        transitionTimeline
            .to('#page4', {
                opacity: 0,
                duration: 1,
                ease: 'power2.inOut'
            })
            .to('.horizontal-scroll-container', {
                opacity: 1,
                pointerEvents: 'auto',
                duration: 1,
                ease: 'power2.inOut'
            }, '<0.5')
            .to(['.scroll-progress-indicator', '.mucha-cursor'], {
                opacity: 1,
                duration: 0.5,
                ease: 'power2.out'
            }, '<0.5');

        // 启动主要内容的动画
        setTimeout(() => {
            // 触发主页面的入场动画
            this.triggerMainPageAnimation();
        }, 500);
    }

    triggerMainPageAnimation() {
        // 触发第一个页面的动画
        const firstPage = document.getElementById('page1');
        if (firstPage) {
            // 添加入场动画类
            firstPage.classList.add('page-enter');
            
            // 如果有现有的页面动画系统，在这里触发
            if (window.pageAnimations && window.pageAnimations.animatePageEntry) {
                window.pageAnimations.animatePageEntry(firstPage);
            }
        }
    }

    // 提供外部接口跳过动画
    skip() {
        if (this.timeline) {
            this.timeline.kill();
        }
        this.transitionToMainContent();
    }
}

// 初始化开场动画
const openingAnimation = new OpeningAnimation();

// 导出到全局作用域，方便调试
window.openingAnimation = openingAnimation;
