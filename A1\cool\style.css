* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Cormorant Garamond", "Times New Roman", serif;
    background: linear-gradient(135deg, #f4e4bc 0%, #e8d5b7 50%, #d4af8c 100%);
    background-attachment: fixed;
}

/* 色卡样式 - 简化设计，提升协调性 */
.color-card {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    transition: opacity 0.3s ease;
}

/* 慕夏风格色彩定义 - 统一协调的色彩系统 */
.color-1 {
    background: linear-gradient(135deg, #f4e4bc 0%, #e8d5b7 100%);
}

.color-2 {
    background: linear-gradient(135deg, #e8d5b7 0%, #d4af8c 100%);
}

.color-3 {
    background: linear-gradient(135deg, #d4af8c 0%, #c9a96e 100%);
}

.color-4 {
    background: linear-gradient(135deg, #c9a96e 0%, #b8956a 100%);
}

.color-5 {
    background: linear-gradient(135deg, #b8956a 0%, #a67c5a 100%);
}

.color-6 {
    background: linear-gradient(135deg, #a67c5a 0%, #8b6f47 100%);
}

.color-7 {
    background: linear-gradient(135deg, #8b6f47 0%, #7a6b53 100%);
}

.color-8 {
    background: linear-gradient(135deg, #7a6b53 0%, #6b5b73 100%);
}

.color-9 {
    background: linear-gradient(135deg, #6b5b73 0%, #8b7e8e 100%);
}

.color-banner-1 {
    background: linear-gradient(135deg, #f4e4bc 0%, #d4af8c 100%);
}

.color-banner-2 {
    background: linear-gradient(135deg, #c9a96e 0%, #8b6f47 100%);
}

img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
}

p {
    text-transform: capitalize;
    font-family: "Cormorant Garamond", serif;
    font-size: 0.9rem;
    font-style: italic;
    color: #8b6f47;
}

a {
    text-decoration: none;
    text-transform: capitalize;
    font-family: "Cormorant Garamond", serif;
    font-size: 0.9rem;
    color: #8b6f47;
    transition: color 0.3s ease;
}

a:hover {
    color: #d4af8c;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100svh;
    padding: 2em;
    background: linear-gradient(45deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(212, 175, 140, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(244, 228, 188, 0.1) 0%, transparent 50%);
    color: #f4e4bc;
    display: flex;
    gap: 2em;
    overflow: hidden;
    border: 3px solid #d4af8c;
    box-shadow: inset 0 0 50px rgba(212, 175, 140, 0.2);
}

.projects,
.loader,
.locations {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2rem;
}

.loader {
    align-items: center;
    gap: 0;
}

.loader h1 {
    text-align: center;
    text-transform: capitalize;
    font-family: "Cinzel Decorative", "Cormorant Garamond", serif;
    font-size: 3rem;
    font-weight: 600;
    line-height: 0.9;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-image: linear-gradient(0deg, #8b6f47, #8b6f47 50%, #f4e4bc 0);
    background-size: 100% 200%;
    background-position: 0% 100%;
    color: #8b6f47;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 2px;
}

.projects-header,
.project-item,
.locations-header,
.location-item {
    opacity: 0;
    display: flex;
    gap: 2em;
    padding: 0.5em 0;
    border-bottom: 1px solid rgba(212, 175, 140, 0.3);
}

.projects-header>*,
.locations-header>* {
    flex: 1;
}

.locations {
    align-items: center;
}

.locations-header,
.location-item {
    width: 50%;
}

.project-item,
.location-item {
    color: #d4af8c;
    font-weight: 300;
}

.image-grid {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    gap: 0.8em;
    z-index: 2;
    border-radius: 12px;
    overflow: hidden;
}

.grid-row {
    width: 100%;
    display: flex;
    gap: 0.8em;
}

/* 图片容器优化 - 简洁设计 */
.img {
    position: relative;
    flex: 1;
    aspect-ratio: 1;
    clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%);
    border-radius: 6px;
    overflow: hidden;
    background: #f4e4bc;
}

.img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 140, 0.1) 0%, transparent 50%, rgba(244, 228, 188, 0.1) 100%);
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.img:hover::before {
    opacity: 0.3;
}

nav {
    position: fixed;
    width: 100vw;
    display: flex;
    padding: 1em 2em;
    gap: 2em;
    background: rgba(244, 228, 188, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid #d4af8c;
    box-shadow: 0 2px 20px rgba(212, 175, 140, 0.3);
}

nav>* {
    flex: 1;
}

.links {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.nav-logo {
    text-align: center;
    display: flex;
    justify-content: center;
}

.nav-logo a {
    font-family: "Cinzel Decorative", "Cormorant Garamond", serif;
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 0.9;
    color: #8b6f47;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.banner-img {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 20%;
    aspect-ratio: 4/5;
    border-radius: 12px;
    overflow: hidden;
}

.intro-copy {
    position: absolute;
    top: 45%;
    transform: translateY(-50%);
    width: 100%;
    padding: 0 8em;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.intro-copy::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 40px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 40"><path d="M10,20 Q50,5 100,20 T190,20" stroke="%23d4af8c" stroke-width="2" fill="none" opacity="0.6"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.title {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
}

.intro-copy h3,
.title h1 {
    position: relative;
    text-transform: capitalize;
    font-family: "Cinzel Decorative", "Cormorant Garamond", serif;
    color: #8b6f47;
    font-weight: 600;
    line-height: 0.9;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.title h1 {
    font-size: 3.5rem;
    background: linear-gradient(135deg, #8b6f47 0%, #d4af8c 50%, #f4e4bc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.intro-copy h3 {
    font-size: 1.8rem;
    color: #8b6f47;
}

.intro-copy h3,
.title h1 {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.intro-copy h3 .word,
.title h1 .word {
    display: inline-block;
    position: relative;
    will-change: transform;
    margin-right: 0.1rem;
} 

@media (max-width: 900px) {
    .loader {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .projects,
    .locations,
    .intro-copy,
    .banner-img {
        display: none;
    }

    .title {
        width: 100%;
        bottom: 20%;
        display: flex;
        justify-content: center;
    }

    .title h1 {
        font-size: 2.5rem;
    }

    .image-grid {
        width: 75%;
        gap: 0.5em;
    }

    .grid-row {
        gap: 0.5em;
        width: 95%;
        justify-content: space-around;
    }
}

/* 慕夏风格装饰元素 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(212, 175, 140, 0.1) 0%, transparent 20%),
        radial-gradient(circle at 80% 80%, rgba(244, 228, 188, 0.1) 0%, transparent 20%),
        radial-gradient(circle at 40% 70%, rgba(139, 111, 71, 0.05) 0%, transparent 15%);
    pointer-events: none;
    z-index: -1;
}

/* 装饰性边框元素 */
.overlay::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 2px solid rgba(212, 175, 140, 0.3);
    border-radius: 20px;
    pointer-events: none;
}

/* 图片悬停效果 - 简化 */
.img img {
    transition: transform 0.3s ease;
}

.img:hover img {
    transform: scale(1.02);
}

/* 导航链接简化效果 */
.links a {
    position: relative;
    transition: color 0.3s ease;
}

.links a:hover {
    color: #c9a96e;
}